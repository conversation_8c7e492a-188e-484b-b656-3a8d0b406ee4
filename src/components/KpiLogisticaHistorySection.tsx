import React, { useState, useMemo, memo, useCallback, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { Search, BarChart3, Edit, Trash2, Eye, EyeOff, Filter, Download, ChevronLeft, ChevronRight, ArrowUp, ArrowDown, ChevronDown } from "lucide-react";
import { type KpiLogisticaData } from "@/app/actions/kpis-logistica";
import ConfirmDialog from "@/components/ui/ConfirmDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  role?: string;
}

interface KpiLogisticaHistorySectionProps {
  kpisLogistica: KpiLogisticaData[];
  loadingKpis: boolean;
  user: User;
  onEditKpi: (kpi: KpiLogisticaData) => void;
  onDeleteKpi: (kpiId: string) => Promise<void>;
  onRefresh: () => Promise<void>;
  showAnalytics?: boolean;
  showFilters?: boolean;
  selectedKpis?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
}

// Componente Tooltip personalizado
const Tooltip = ({ children, content }: { children: React.ReactNode; content: string }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const tooltipRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  const updatePosition = useCallback(() => {
    if (triggerRef.current && tooltipRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
      let y = triggerRect.top - tooltipRect.height - 8;

      // Ajustar si se sale del viewport horizontalmente
      if (x < 8) x = 8;
      if (x + tooltipRect.width > viewportWidth - 8) x = viewportWidth - tooltipRect.width - 8;

      // Ajustar si se sale del viewport verticalmente
      if (y < 8) y = triggerRect.bottom + 8;

      setPosition({ x, y });
    }
  }, []);

  useEffect(() => {
    if (isVisible) {
      updatePosition();
      window.addEventListener('scroll', updatePosition);
      window.addEventListener('resize', updatePosition);
      return () => {
        window.removeEventListener('scroll', updatePosition);
        window.removeEventListener('resize', updatePosition);
      };
    }
  }, [isVisible, updatePosition]);

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        className="inline-block"
      >
        {children}
      </div>
      {isVisible && createPortal(
        <div
          ref={tooltipRef}
          className="fixed z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg pointer-events-none"
          style={{
            left: `${position.x}px`,
            top: `${position.y}px`,
          }}
        >
          {content}
        </div>,
        document.body
      )}
    </>
  );
};

// Función para formatear fechas
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// Función para formatear números con separadores de miles
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('es-ES').format(num);
};

// Función para formatear moneda
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'MXN',
    minimumFractionDigits: 2
  }).format(amount);
};

const KpiLogisticaHistorySection: React.FC<KpiLogisticaHistorySectionProps> = ({
  kpisLogistica,
  loadingKpis,
  user,
  onEditKpi,
  onDeleteKpi,
  onRefresh,
  showAnalytics: externalShowAnalytics = false,
  showFilters: externalShowFilters = false,
  selectedKpis: externalSelectedKpis = [],
  onSelectionChange
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedYear, setSelectedYear] = useState<number | "all">("all");
  const [sortField, setSortField] = useState<keyof KpiLogisticaData>("weekNumber");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [selectedKpis, setSelectedKpis] = useState<string[]>(externalSelectedKpis);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [kpiToDelete, setKpiToDelete] = useState<KpiLogisticaData | null>(null);
  const [openDropdowns, setOpenDropdowns] = useState<Set<string>>(new Set());

  // Sincronizar con props externas
  useEffect(() => {
    setSelectedKpis(externalSelectedKpis);
  }, [externalSelectedKpis]);

  // Obtener años únicos para el filtro
  const availableYears = useMemo(() => {
    const years = [...new Set(kpisLogistica.map(kpi => kpi.year))].sort((a, b) => b - a);
    return years;
  }, [kpisLogistica]);

  // Filtrar y ordenar KPIs
  const filteredAndSortedKpis = useMemo(() => {
    let filtered = kpisLogistica.filter(kpi => {
      const matchesSearch = searchTerm === "" || 
        `Semana ${kpi.weekNumber}/${kpi.year}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kpi.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kpi.user?.email?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesYear = selectedYear === "all" || kpi.year === selectedYear;
      
      return matchesSearch && matchesYear;
    });

    // Ordenar
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Manejar casos especiales para ordenamiento
      if (sortField === 'user') {
        aValue = a.user?.name || a.user?.email || '';
        bValue = b.user?.name || b.user?.email || '';
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    return filtered;
  }, [kpisLogistica, searchTerm, selectedYear, sortField, sortDirection]);

  // Paginación
  const totalPages = Math.ceil(filteredAndSortedKpis.length / itemsPerPage);
  const paginatedKpis = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredAndSortedKpis.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredAndSortedKpis, currentPage, itemsPerPage]);

  // Funciones de manejo
  const handleSort = (field: keyof KpiLogisticaData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const handleSelectKpi = useCallback((kpiId: string) => {
    const newSelection = selectedKpis.includes(kpiId)
      ? selectedKpis.filter(id => id !== kpiId)
      : [...selectedKpis, kpiId];
    
    setSelectedKpis(newSelection);
    onSelectionChange?.(newSelection);
  }, [selectedKpis, onSelectionChange]);

  const handleSelectAll = useCallback(() => {
    const currentPageIds = paginatedKpis.map(kpi => kpi.id!);
    const allSelected = currentPageIds.every(id => selectedKpis.includes(id));
    
    let newSelection;
    if (allSelected) {
      newSelection = selectedKpis.filter(id => !currentPageIds.includes(id));
    } else {
      newSelection = [...new Set([...selectedKpis, ...currentPageIds])];
    }
    
    setSelectedKpis(newSelection);
    onSelectionChange?.(newSelection);
  }, [paginatedKpis, selectedKpis, onSelectionChange]);

  const handleDeleteConfirmation = (kpi: KpiLogisticaData) => {
    setKpiToDelete(kpi);
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (kpiToDelete?.id) {
      await onDeleteKpi(kpiToDelete.id);
      setShowDeleteConfirm(false);
      setKpiToDelete(null);
    }
  };

  // Resetear página cuando cambian los filtros
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedYear]);

  // Componente de fila de tabla memoizado para mejor rendimiento
  const TableRow = memo(({
    kpi,
    isSelected,
    onSelect,
    onEdit,
    onDelete,
    user: currentUser
  }: {
    kpi: KpiLogisticaData;
    isSelected: boolean;
    onSelect: (id: string) => void;
    onEdit: (kpi: KpiLogisticaData) => void;
    onDelete: (id: string) => void;
    user: User;
  }) => {
    return (
      <tr className="bg-white shadow hover:shadow-md transition-all duration-200 rounded-xl border border-gray-200" style={{ overflow: 'visible' }}>
        <td className="px-4 py-3 rounded-l-xl">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onSelect(kpi.id!)}
            className="w-4 h-4 rounded-xl border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
          />
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="flex items-center space-x-3">
            <div>
              <div className="text-sm font-medium text-gray-900">
                Semana {kpi.weekNumber}/{kpi.year}
              </div>
              <div className="text-xs text-gray-500">
                {formatDate(kpi.weekStartDate)} - {formatDate(kpi.weekEndDate)}
              </div>
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900">
            <div className="font-medium">{formatNumber(kpi.unidadesConfirmadas)} / {formatNumber(kpi.unidadesSolicitadas)}</div>
            <div className="text-xs text-gray-500">
              {((kpi.unidadesConfirmadas / kpi.unidadesSolicitadas) * 100).toFixed(1)}% confirmadas
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900">
            <div className="font-medium text-green-600">{kpi.porcentajeEntregasTiempo.toFixed(1)}%</div>
            <div className="text-xs text-gray-500">
              {kpi.porcentajeRetardos.toFixed(1)}% retardos
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium">
            {formatCurrency(kpi.promedioCostoFleteLitro)}
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium">
            {formatCurrency(kpi.pagoSemanalFlete)}
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900">
            <div className="font-medium">{kpi.user?.name || 'Usuario'}</div>
            <div className="text-xs text-gray-500">
              {formatDate(kpi.createdAt || new Date().toISOString())}
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium rounded-r-xl">
          <div className="flex items-center justify-end space-x-2">
            <button
              onClick={() => onEdit(kpi)}
              className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
              title="Editar"
            >
              <Edit className="h-4 w-4" />
            </button>
            {(currentUser.role === "ADMIN" || currentUser.role === "SUPER_ADMIN") && (
              <button
                onClick={() => onDelete(kpi.id!)}
                className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                title="Eliminar"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>
        </td>
      </tr>
    );
  });

  return (
    <div className="space-y-6">
      {/* Filtros y controles */}
      {externalShowFilters && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Búsqueda */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Buscar por semana, usuario..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>

            {/* Filtro por año */}
            <div className="w-full sm:w-48">
              <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(value === "all" ? "all" : parseInt(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="Filtrar por año" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los años</SelectItem>
                  {availableYears.map(year => (
                    <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      {kpisLogistica.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-3">
            <BarChart3 className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay KPIs registrados</h3>
          <p className="text-gray-500 mb-2">Comienza agregando datos semanales para ver el historial</p>
        </div>
      ) : filteredAndSortedKpis.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron resultados</h3>
          <p className="text-gray-500 mb-4">Intenta ajustar los filtros de búsqueda</p>
          <button
            onClick={() => {
              setSearchTerm("");
              setSelectedYear("all");
              setCurrentPage(1);
            }}
            className="text-primary hover:text-primary/80 font-medium"
          >
            Limpiar filtros
          </button>
        </div>
      ) : (
        <>
          {/* Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full border-separate border-spacing-y-2">
              <thead>
                <tr>
                  <th className="px-4 py-1 text-left bg-transparent">
                    <input
                      type="checkbox"
                      checked={paginatedKpis.length > 0 && paginatedKpis.every(kpi => selectedKpis.includes(kpi.id!))}
                      ref={(input) => {
                        if (input) {
                          const currentPageIds = paginatedKpis.map(kpi => kpi.id!);
                          const selectedCurrentPage = currentPageIds.filter(id => selectedKpis.includes(id));
                          input.indeterminate = selectedCurrentPage.length > 0 && selectedCurrentPage.length < currentPageIds.length;
                        }
                      }}
                      onChange={handleSelectAll}
                      className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
                    />
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <button
                      onClick={() => handleSort('weekNumber')}
                      className="flex items-center space-x-1 hover:text-gray-600"
                    >
                      <span>Semana</span>
                      {sortField === 'weekNumber' && (
                        sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                      )}
                    </button>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Unidades Confirmadas vs Solicitadas">
                      <span>Unidades</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Porcentaje de Entregas a Tiempo">
                      <span>% Entregas</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Costo Promedio de Flete por Litro">
                      <span>Costo/L</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Pago Semanal de Flete">
                      <span>Pago Flete</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Usuario que registró los datos">
                      <span>Usuario</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-right text-sm font-normal text-gray-400 bg-transparent">
                    <span>Acciones</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedKpis.map((kpi) => (
                  <TableRow
                    key={kpi.id}
                    kpi={kpi}
                    isSelected={selectedKpis.includes(kpi.id!)}
                    onSelect={handleSelectKpi}
                    onEdit={onEditKpi}
                    onDelete={() => handleDeleteConfirmation(kpi)}
                    user={user}
                  />
                ))}
              </tbody>
            </table>
          </div>

          {/* Paginación */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
              <div className="flex justify-between flex-1 sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Anterior
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative ml-3 inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Siguiente
                </button>
              </div>
              <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Mostrando{' '}
                    <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span>
                    {' '}a{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * itemsPerPage, filteredAndSortedKpis.length)}
                    </span>
                    {' '}de{' '}
                    <span className="font-medium">{filteredAndSortedKpis.length}</span>
                    {' '}resultados
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="h-5 w-5" />
                    </button>

                    {/* Números de página */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === pageNum
                              ? 'z-10 bg-primary border-primary text-white'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}

                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleConfirmDelete}
        title="Eliminar KPI de Logística"
        message={`¿Estás seguro de que deseas eliminar los datos de la Semana ${kpiToDelete?.weekNumber}/${kpiToDelete?.year}? Esta acción no se puede deshacer.`}
        confirmText="Eliminar"
        cancelText="Cancelar"
        type="danger"
      />
    </div>
  );
};

export default KpiLogisticaHistorySection;
